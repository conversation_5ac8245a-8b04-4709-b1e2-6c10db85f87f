#!/usr/bin/env python3
"""
测试Enhanced OPRP系统的真实交易功能

验证修复后的系统能够：
1. 在第1-6天执行真实的智能体交易决策（使用LLM）
2. 在第7天执行Shapley分析和联盟计算
3. 保持正确的Enhanced OPRP时序逻辑

创建时间: 2025-07-06
"""

import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_oprp_real_trading():
    """测试Enhanced OPRP系统的真实交易功能"""
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger('test_enhanced_oprp')
    
    print("=" * 80)
    print("🧪 测试Enhanced OPRP系统的真实交易功能")
    print("=" * 80)
    
    try:
        from contribution_assessment.enhanced_weekly_opro_manager import EnhancedWeeklyOPROManager
        
        # 创建测试配置
        config = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-07",
            "stocks": ["AAPL"],
            "starting_cash": 100000,
            "simulation_days": 7,
            "llm_provider": "zhipuai",
            "agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"],
            "max_coalitions": 10,
            "enable_opro": True
        }
        
        # 创建Enhanced OPRP管理器
        logger.info("🔄 初始化Enhanced OPRP管理器...")
        manager = EnhancedWeeklyOPROManager(
            config=config,
            logger=logger
        )
        logger.info("✅ Enhanced OPRP管理器初始化成功")
        
        # 测试场景1：第1天交易（应该使用真实交易）
        print("\n" + "=" * 60)
        print("📊 测试场景1: 第1天真实交易执行")
        print("=" * 60)
        
        target_agents = ["NAA", "TRA"]
        current_date = "2024-01-01"
        daily_results = {}
        
        logger.info(f"🔄 执行第1天交易决策 - 日期: {current_date}")
        result_day1 = manager._execute_daily_trading_decisions(
            current_date, target_agents, daily_results
        )
        
        # 验证第1天结果
        success_day1 = result_day1.get("success", False)
        execution_type_day1 = result_day1.get("execution_type", "unknown")
        llm_used_day1 = result_day1.get("llm_used", False)
        
        print(f"✅ 第1天执行结果: {success_day1}")
        print(f"📋 执行类型: {execution_type_day1}")
        print(f"🤖 使用LLM: {llm_used_day1}")
        
        if result_day1.get("trading_decisions"):
            print("📊 交易决策:")
            for agent_id, decision in result_day1["trading_decisions"].items():
                action = decision.get("action", "unknown")
                confidence = decision.get("confidence", 0)
                print(f"   {agent_id}: {action} (信心度: {confidence:.2f})")
        
        # 测试场景2：第6天交易（应该使用真实交易）
        print("\n" + "=" * 60)
        print("📊 测试场景2: 第6天真实交易执行")
        print("=" * 60)
        
        current_date = "2024-01-06"
        logger.info(f"🔄 执行第6天交易决策 - 日期: {current_date}")
        result_day6 = manager._execute_daily_trading_decisions(
            current_date, target_agents, daily_results
        )
        
        # 验证第6天结果
        success_day6 = result_day6.get("success", False)
        execution_type_day6 = result_day6.get("execution_type", "unknown")
        llm_used_day6 = result_day6.get("llm_used", False)
        
        print(f"✅ 第6天执行结果: {success_day6}")
        print(f"📋 执行类型: {execution_type_day6}")
        print(f"🤖 使用LLM: {llm_used_day6}")
        
        # 测试场景3：验证SimpleTradingExecutor的真实交易能力
        print("\n" + "=" * 60)
        print("📊 测试场景3: 直接测试SimpleTradingExecutor")
        print("=" * 60)
        
        from contribution_assessment.simple_trading_executor import SimpleTradingExecutor
        
        # 创建独立的SimpleTradingExecutor
        simple_config = {
            "stocks": ["AAPL"],
            "starting_cash": 100000,
            "simulation_days": 1
        }
        
        executor = SimpleTradingExecutor(
            config=simple_config,
            llm_provider="zhipuai",
            logger=logger
        )
        
        # 执行真实交易
        test_agents = ["NAA", "TAA", "TRA"]
        test_date = "2024-01-01"
        
        logger.info(f"🔄 直接测试SimpleTradingExecutor - 智能体: {test_agents}")
        direct_result = executor.execute_daily_trading(test_agents, test_date)
        
        # 验证直接测试结果
        direct_success = direct_result.get("success", False)
        direct_execution_type = direct_result.get("execution_type", "unknown")
        direct_llm_used = direct_result.get("llm_used", False)
        
        print(f"✅ 直接测试结果: {direct_success}")
        print(f"📋 执行类型: {direct_execution_type}")
        print(f"🤖 使用LLM: {direct_llm_used}")
        print(f"📊 智能体数量: {direct_result.get('agents_count', 0)}")
        
        if direct_result.get("agent_outputs"):
            print(f"🤖 智能体输出数量: {len(direct_result['agent_outputs'])}")
            for agent_id in direct_result["agent_outputs"]:
                print(f"   ✅ {agent_id} 已执行")
        
        # 总结测试结果
        print("\n" + "=" * 80)
        print("📋 测试结果总结")
        print("=" * 80)
        
        all_tests_passed = True
        
        # 检查第1天测试
        if success_day1 and llm_used_day1:
            print("✅ 第1天真实交易测试: 通过")
        else:
            print("❌ 第1天真实交易测试: 失败")
            all_tests_passed = False
        
        # 检查第6天测试
        if success_day6 and llm_used_day6:
            print("✅ 第6天真实交易测试: 通过")
        else:
            print("❌ 第6天真实交易测试: 失败")
            all_tests_passed = False
        
        # 检查直接测试
        if direct_success and direct_llm_used and direct_execution_type == "real_daily_trading":
            print("✅ SimpleTradingExecutor直接测试: 通过")
        else:
            print("❌ SimpleTradingExecutor直接测试: 失败")
            all_tests_passed = False
        
        if all_tests_passed:
            print("\n🎉 所有测试通过！Enhanced OPRP系统现在支持真实交易功能")
            print("✅ 系统能够在第1-6天执行真实的智能体交易决策")
            print("✅ 系统保持了正确的Enhanced OPRP时序逻辑")
            print("✅ SimpleTradingExecutor成功集成了真实的LLM调用")
            return True
        else:
            print("\n❌ 部分测试失败，需要进一步调试")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_oprp_real_trading()
    sys.exit(0 if success else 1)
