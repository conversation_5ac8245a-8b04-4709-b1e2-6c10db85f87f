2025-07-06 21:17:05,367 - __main__ - INFO - ====================================================================================================
2025-07-06 21:17:05,367 - __main__ - INFO - OPRO系统启动
2025-07-06 21:17:05,367 - __main__ - INFO - ====================================================================================================
2025-07-06 21:17:05,368 - __main__ - INFO - 运行模式: integrated
2025-07-06 21:17:05,368 - __main__ - INFO - LLM提供商: zhipuai
2025-07-06 21:17:05,368 - __main__ - INFO - OPRO启用: True
2025-07-06 21:17:05,368 - __main__ - INFO - 数据存储启用: True
2025-07-06 21:17:05,368 - __main__ - INFO - 代理日志记录启用: True
2025-07-06 21:17:05,369 - __main__ - INFO - 初始化系统...
2025-07-06 21:17:05,369 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 21:17:05,369 - __main__ - INFO - 代理交互日志记录器初始化完成 (启用: True, 使用交易日期: True)
2025-07-06 21:17:05,369 - __main__ - DEBUG - 确保目录结构存在: data\trading\2025-07-06
2025-07-06 21:17:05,370 - __main__ - INFO - 设置实验日期: 2025-07-06
2025-07-06 21:17:05,370 - __main__ - INFO - 代理交互日志记录器初始化成功 (实验日期: 2025-07-06)
2025-07-06 21:17:05,370 - __main__ - DEBUG - 确保目录存在: data
2025-07-06 21:17:05,370 - __main__ - DEBUG - 确保目录存在: data/trading
2025-07-06 21:17:05,370 - __main__ - DEBUG - 确保目录存在: data/prompts
2025-07-06 21:17:05,370 - __main__ - DEBUG - 确保目录存在: data/visualizations
2025-07-06 21:17:05,370 - __main__ - DEBUG - 确保目录存在: data/exports
2025-07-06 21:17:05,370 - __main__ - DEBUG - 确保目录存在: data/backups
2025-07-06 21:17:05,383 - __main__ - INFO - 数据库初始化完成
2025-07-06 21:17:05,384 - __main__ - INFO - 自动备份线程已启动
2025-07-06 21:17:05,384 - __main__ - INFO - 综合数据存储管理器初始化完成
2025-07-06 21:17:05,384 - __main__ - INFO - 交易数据收集器初始化完成
2025-07-06 21:17:05,384 - __main__ - INFO - 交易数据提取器初始化完成
2025-07-06 21:17:05,399 - __main__ - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 21:17:05,400 - __main__ - INFO - 提示词优化跟踪器初始化完成
2025-07-06 21:17:05,400 - __main__ - INFO - 可视化管理器初始化完成
2025-07-06 21:17:05,413 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 21:17:05,414 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 21:17:05,414 - __main__ - INFO - 数据分析工具初始化完成
2025-07-06 21:17:05,415 - __main__ - DEBUG - 备份目录初始化完成: data/backups
2025-07-06 21:17:05,416 - __main__ - INFO - 自动备份已启动，间隔: 24 小时
2025-07-06 21:17:05,416 - __main__ - INFO - 备份管理器初始化完成
2025-07-06 21:17:05,416 - __main__ - INFO - 所有数据存储组件初始化完成
2025-07-06 21:17:05,416 - __main__ - INFO - 集成数据管理器初始化完成 (启用: True)
2025-07-06 21:17:05,416 - __main__ - INFO - 集成数据管理器初始化成功
2025-07-06 21:17:05,431 - contribution_assessment.weekly_shapley_trigger - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 21:17:05,432 - contribution_assessment.weekly_shapley_trigger - INFO - 联盟实验跟踪器初始化完成
2025-07-06 21:17:05,432 - contribution_assessment.weekly_shapley_trigger - INFO - 周期性Shapley触发器初始化完成
2025-07-06 21:17:05,433 - contribution_assessment.enhanced_shapley_storage_manager - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 21:17:05,433 - contribution_assessment.assessor.ContributionAssessor - INFO - 分析缓存初始化完成
2025-07-06 21:17:05,434 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟管理器初始化完成
2025-07-06 21:17:05,434 - contribution_assessment.assessor.ContributionAssessor - INFO - 联盟存储管理器初始化完成，数据目录: data
2025-07-06 21:17:05,435 - contribution_assessment.assessor.ContributionAssessor - INFO - 交易模拟器初始化完成
2025-07-06 21:17:05,435 - contribution_assessment.assessor.ContributionAssessor - INFO - Shapley值计算器初始化完成
2025-07-06 21:17:05,435 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 21:17:05,437 - contribution_assessment.assessor.ContributionAssessor - INFO - 自动备份线程已启动
2025-07-06 21:17:05,437 - contribution_assessment.assessor.ContributionAssessor - INFO - 综合数据存储管理器初始化完成
2025-07-06 21:17:05,448 - contribution_assessment.assessor.ContributionAssessor - INFO - 加载了 7 个智能体的提示词历史
2025-07-06 21:17:05,449 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化跟踪器初始化完成
2025-07-06 21:17:05,450 - contribution_assessment.assessor.ContributionAssessor - INFO - 尝试初始化LLM提供商: zhipuai
2025-07-06 21:17:05,450 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 21:17:05,454 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 21:17:05,592 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 21:17:05,593 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 21:17:05,716 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-07-06 21:17:05,717 - httpx - DEBUG - load_verify_locations cafile='E:\\ProgramData\\miniconda3\\Library\\ssl\\cacert.pem'
2025-07-06 21:17:05,847 - contribution_assessment.assessor.ContributionAssessor - INFO - ZhipuAI 客户端初始化成功
2025-07-06 21:17:05,848 - contribution_assessment.assessor.ContributionAssessor - INFO - 数据库初始化完成
2025-07-06 21:17:05,955 - contribution_assessment.assessor.ContributionAssessor - INFO - 最新Shapley数据加载完成
2025-07-06 21:17:05,956 - contribution_assessment.assessor.ContributionAssessor - INFO - 历史得分管理器初始化完成
2025-07-06 21:17:05,956 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO优化器初始化完成
2025-07-06 21:17:05,956 - contribution_assessment.assessor.ContributionAssessor - INFO - OPRO组件初始化成功
2025-07-06 21:17:05,956 - contribution_assessment.assessor.ContributionAssessor - INFO - 贡献度评估器初始化完成 (OPRO: 启用)
2025-07-06 21:17:05,957 - __main__ - INFO - 每周OPRO管理器初始化完成，数据目录: data\trading
2025-07-06 21:17:05,964 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据库初始化完成
2025-07-06 21:17:05,965 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 自动备份线程已启动
2025-07-06 21:17:05,965 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 综合数据存储管理器初始化完成
2025-07-06 21:17:05,966 - __main__ - INFO - 加载了 0 个活跃A/B测试
2025-07-06 21:17:05,966 - __main__ - INFO - A/B测试框架初始化完成
2025-07-06 21:17:05,968 - __main__ - INFO - 双轨实验系统初始化完成
2025-07-06 21:17:05,968 - __main__ - INFO - Shapley值计算器初始化完成
2025-07-06 21:17:05,968 - __main__ - INFO - 增强的Shapley存储管理器初始化完成，分析目录: data\trading\shapley_analysis
2025-07-06 21:17:05,972 - __main__ - INFO - 加载历史数据完成: 16 个实验记录
2025-07-06 21:17:05,972 - __main__ - INFO - 迭代Shapley值计算器初始化完成
2025-07-06 21:17:05,972 - __main__ - INFO - 增强的周期性OPRO管理器初始化完成
2025-07-06 21:17:05,973 - __main__ - INFO - 配置: 7天连续优化周期, 基线运行=7天, A/B测试=7天, 连续优化=True
2025-07-06 21:17:05,973 - __main__ - INFO - ✅ 增强OPRP管理器初始化成功
2025-07-06 21:17:05,973 - __main__ - INFO - 系统初始化完成
2025-07-06 21:17:05,973 - __main__ - INFO - ================================================================================
2025-07-06 21:17:05,974 - __main__ - INFO - 运行模式: 增强集成模式（7天连续优化循环）
2025-07-06 21:17:05,974 - __main__ - INFO - ================================================================================
2025-07-06 21:17:05,974 - __main__ - INFO - 开始运行增强的完整日期范围交易系统: 2025-01-01 到 2025-03-31
2025-07-06 21:17:05,974 - __main__ - INFO - ====================================================================================================
2025-07-06 21:17:05,974 - __main__ - INFO - 🚀 启动增强的完整日期范围交易系统（7天连续优化循环）
2025-07-06 21:17:05,975 - __main__ - INFO - ====================================================================================================
2025-07-06 21:17:05,975 - __main__ - INFO - 📅 交易期间: 2025-01-01 到 2025-03-31
2025-07-06 21:17:05,975 - __main__ - INFO - 🤖 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,975 - __main__ - INFO - 🔄 增强OPRP: 启用（7天连续优化循环）
2025-07-06 21:17:05,976 - __main__ - INFO - 步骤1: 生成7天周期的交易日期列表...
2025-07-06 21:17:05,977 - __main__ - INFO - 📅 生成交易日期范围: 2025-01-01 到 2025-03-31
2025-07-06 21:17:05,977 - __main__ - INFO - 📊 总交易日数: 64
2025-07-06 21:17:05,977 - __main__ - INFO - 🗓️  首个交易日: 2025-01-01
2025-07-06 21:17:05,978 - __main__ - INFO - 🗓️  最后交易日: 2025-03-31
2025-07-06 21:17:05,978 - __main__ - INFO - 步骤2: 按7天周期分组处理...
2025-07-06 21:17:05,978 - __main__ - INFO - 📊 生成了 9 个7天周期
2025-07-06 21:17:05,978 - __main__ - INFO -    - 基线运行周: 5 个
2025-07-06 21:17:05,978 - __main__ - INFO -    - A/B测试周: 4 个
2025-07-06 21:17:05,978 - __main__ - INFO - 步骤3: 开始7天连续优化循环...
2025-07-06 21:17:05,978 - __main__ - INFO - 🔄 开始第 1 周（baseline_operation）: 2025-01-01 到 2025-01-09
2025-07-06 21:17:05,979 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-01
2025-07-06 21:17:05,979 - __main__ - INFO - 🆕 开始第1周 - 基线运行阶段: 2025-01-01
2025-07-06 21:17:05,979 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第1/7天
2025-07-06 21:17:05,979 - __main__ - INFO - 📊 基线运行周进行中 - 第1周，第1/7天，执行日常交易决策
2025-07-06 21:17:05,980 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-01
2025-07-06 21:17:05,980 - __main__ - INFO - SimpleTradingExecutor 初始化完成
2025-07-06 21:17:05,981 - __main__ - INFO - 📊 执行简化智能体交易决策 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,981 - __main__ - INFO - 🔄 执行简化每日交易决策 - 日期: 2025-01-01
2025-07-06 21:17:05,981 - __main__ - INFO - 📋 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,981 - __main__ - INFO - 📊 NAA 交易决策: hold (信心度: 0.48)
2025-07-06 21:17:05,981 - __main__ - INFO - 📊 TAA 交易决策: sell (信心度: 0.43)
2025-07-06 21:17:05,981 - __main__ - INFO - 📊 FAA 交易决策: buy (信心度: 0.44)
2025-07-06 21:17:05,982 - __main__ - INFO - 📊 BOA 交易决策: hold (信心度: 0.65)
2025-07-06 21:17:05,982 - __main__ - INFO - 📊 BeOA 交易决策: hold (信心度: 0.74)
2025-07-06 21:17:05,982 - __main__ - INFO - 📊 NOA 交易决策: sell (信心度: 0.77)
2025-07-06 21:17:05,982 - __main__ - INFO - 📊 TRA 交易决策: sell (信心度: 0.71)
2025-07-06 21:17:05,982 - __main__ - INFO - ✅ 简化每日交易决策完成 - 日期: 2025-01-01
2025-07-06 21:17:05,983 - __main__ - INFO - 📈 组合表现: 收益率 -0.0035, 风险 0.0043
2025-07-06 21:17:05,983 - __main__ - INFO - ✅ 简化每日交易决策执行成功 - 日期: 2025-01-01
2025-07-06 21:17:05,983 - __main__ - INFO - 📈 组合收益率: -0.0035
2025-07-06 21:17:05,983 - __main__ - INFO - 📊 夏普比率: -0.8071
2025-07-06 21:17:05,983 - __main__ - INFO - 🤖 参与智能体数量: 7
2025-07-06 21:17:05,983 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-02
2025-07-06 21:17:05,983 - __main__ - INFO - 📊 第1周进行中 - 第2/7天，执行日常交易决策
2025-07-06 21:17:05,983 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第2/7天
2025-07-06 21:17:05,983 - __main__ - INFO - 📊 基线运行周进行中 - 第1周，第2/7天，执行日常交易决策
2025-07-06 21:17:05,983 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-02
2025-07-06 21:17:05,984 - __main__ - INFO - SimpleTradingExecutor 初始化完成
2025-07-06 21:17:05,984 - __main__ - INFO - 📊 执行简化智能体交易决策 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,984 - __main__ - INFO - 🔄 执行简化每日交易决策 - 日期: 2025-01-02
2025-07-06 21:17:05,984 - __main__ - INFO - 📋 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,984 - __main__ - INFO - 📊 NAA 交易决策: hold (信心度: 0.62)
2025-07-06 21:17:05,984 - __main__ - INFO - 📊 TAA 交易决策: sell (信心度: 0.73)
2025-07-06 21:17:05,984 - __main__ - INFO - 📊 FAA 交易决策: hold (信心度: 0.62)
2025-07-06 21:17:05,985 - __main__ - INFO - 📊 BOA 交易决策: buy (信心度: 0.78)
2025-07-06 21:17:05,985 - __main__ - INFO - 📊 BeOA 交易决策: hold (信心度: 0.52)
2025-07-06 21:17:05,985 - __main__ - INFO - 📊 NOA 交易决策: hold (信心度: 0.49)
2025-07-06 21:17:05,985 - __main__ - INFO - 📊 TRA 交易决策: buy (信心度: 0.57)
2025-07-06 21:17:05,985 - __main__ - INFO - ✅ 简化每日交易决策完成 - 日期: 2025-01-02
2025-07-06 21:17:05,985 - __main__ - INFO - 📈 组合表现: 收益率 0.0018, 风险 0.0057
2025-07-06 21:17:05,986 - __main__ - INFO - ✅ 简化每日交易决策执行成功 - 日期: 2025-01-02
2025-07-06 21:17:05,986 - __main__ - INFO - 📈 组合收益率: 0.0018
2025-07-06 21:17:05,986 - __main__ - INFO - 📊 夏普比率: 0.3086
2025-07-06 21:17:05,986 - __main__ - INFO - 🤖 参与智能体数量: 7
2025-07-06 21:17:05,986 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-03
2025-07-06 21:17:05,987 - __main__ - INFO - 📊 第1周进行中 - 第3/7天，执行日常交易决策
2025-07-06 21:17:05,987 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第3/7天
2025-07-06 21:17:05,987 - __main__ - INFO - 📊 基线运行周进行中 - 第1周，第3/7天，执行日常交易决策
2025-07-06 21:17:05,987 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-03
2025-07-06 21:17:05,987 - __main__ - INFO - SimpleTradingExecutor 初始化完成
2025-07-06 21:17:05,987 - __main__ - INFO - 📊 执行简化智能体交易决策 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,988 - __main__ - INFO - 🔄 执行简化每日交易决策 - 日期: 2025-01-03
2025-07-06 21:17:05,988 - __main__ - INFO - 📋 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,988 - __main__ - INFO - 📊 NAA 交易决策: hold (信心度: 0.73)
2025-07-06 21:17:05,988 - __main__ - INFO - 📊 TAA 交易决策: buy (信心度: 0.47)
2025-07-06 21:17:05,988 - __main__ - INFO - 📊 FAA 交易决策: hold (信心度: 0.60)
2025-07-06 21:17:05,989 - __main__ - INFO - 📊 BOA 交易决策: buy (信心度: 0.41)
2025-07-06 21:17:05,989 - __main__ - INFO - 📊 BeOA 交易决策: hold (信心度: 0.60)
2025-07-06 21:17:05,989 - __main__ - INFO - 📊 NOA 交易决策: buy (信心度: 0.55)
2025-07-06 21:17:05,989 - __main__ - INFO - 📊 TRA 交易决策: sell (信心度: 0.47)
2025-07-06 21:17:05,989 - __main__ - INFO - ✅ 简化每日交易决策完成 - 日期: 2025-01-03
2025-07-06 21:17:05,990 - __main__ - INFO - 📈 组合表现: 收益率 0.0031, 风险 0.0043
2025-07-06 21:17:05,990 - __main__ - INFO - ✅ 简化每日交易决策执行成功 - 日期: 2025-01-03
2025-07-06 21:17:05,990 - __main__ - INFO - 📈 组合收益率: 0.0031
2025-07-06 21:17:05,990 - __main__ - INFO - 📊 夏普比率: 0.7283
2025-07-06 21:17:05,990 - __main__ - INFO - 🤖 参与智能体数量: 7
2025-07-06 21:17:05,991 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-06
2025-07-06 21:17:05,991 - __main__ - INFO - 📊 第1周进行中 - 第6/7天，执行日常交易决策
2025-07-06 21:17:05,991 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第6/7天
2025-07-06 21:17:05,991 - __main__ - INFO - 📊 基线运行周进行中 - 第1周，第6/7天，执行日常交易决策
2025-07-06 21:17:05,992 - __main__ - INFO - 🤖 开始执行每日智能体交易决策 - 日期: 2025-01-06
2025-07-06 21:17:05,992 - __main__ - INFO - SimpleTradingExecutor 初始化完成
2025-07-06 21:17:05,992 - __main__ - INFO - 📊 执行简化智能体交易决策 - 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,993 - __main__ - INFO - 🔄 执行简化每日交易决策 - 日期: 2025-01-06
2025-07-06 21:17:05,993 - __main__ - INFO - 📋 目标智能体: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,993 - __main__ - INFO - 📊 NAA 交易决策: sell (信心度: 0.68)
2025-07-06 21:17:05,993 - __main__ - INFO - 📊 TAA 交易决策: hold (信心度: 0.54)
2025-07-06 21:17:05,994 - __main__ - INFO - 📊 FAA 交易决策: buy (信心度: 0.61)
2025-07-06 21:17:05,994 - __main__ - INFO - 📊 BOA 交易决策: hold (信心度: 0.52)
2025-07-06 21:17:05,995 - __main__ - INFO - 📊 BeOA 交易决策: hold (信心度: 0.49)
2025-07-06 21:17:05,995 - __main__ - INFO - 📊 NOA 交易决策: hold (信心度: 0.76)
2025-07-06 21:17:05,995 - __main__ - INFO - 📊 TRA 交易决策: hold (信心度: 0.41)
2025-07-06 21:17:05,997 - __main__ - INFO - ✅ 简化每日交易决策完成 - 日期: 2025-01-06
2025-07-06 21:17:05,997 - __main__ - INFO - 📈 组合表现: 收益率 0.0000, 风险 0.0071
2025-07-06 21:17:05,997 - __main__ - INFO - ✅ 简化每日交易决策执行成功 - 日期: 2025-01-06
2025-07-06 21:17:05,997 - __main__ - INFO - 📈 组合收益率: 0.0000
2025-07-06 21:17:05,998 - __main__ - INFO - 📊 夏普比率: 0.0000
2025-07-06 21:17:05,998 - __main__ - INFO - 🤖 参与智能体数量: 7
2025-07-06 21:17:05,998 - __main__ - INFO - 🔄 开始7天连续优化循环 - 日期: 2025-01-07
2025-07-06 21:17:05,998 - __main__ - INFO - ✅ 第1周基线运行完成（第7天），准备执行Shapley分析和提示词优化
2025-07-06 21:17:05,999 - __main__ - INFO - 📊 执行基线运行阶段 - 第1周，第7/7天
2025-07-06 21:17:05,999 - __main__ - INFO - 🎯 基线运行周完成（第7天），开始Shapley分析和提示词优化...
2025-07-06 21:17:05,999 - __main__ - INFO - 🧮 开始迭代Shapley值计算: ['NAA', 'TAA', 'FAA', 'BOA', 'BeOA', 'NOA', 'TRA']
2025-07-06 21:17:05,999 - __main__ - WARNING - ⚠️ 数据质量较低: 0.000
2025-07-06 21:17:06,003 - __main__ - INFO - 从缓存数据构建了 128 个联盟值
2025-07-06 21:17:06,004 - __main__ - INFO - 开始计算 7 个智能体的Shapley值
2025-07-06 21:17:06,005 - __main__ - INFO - 已提供 128 个联盟的特征函数值
2025-07-06 21:17:06,005 - __main__ - DEBUG - 生成了 128 个可能的联盟
2025-07-06 21:17:06,005 - __main__ - INFO - 联盟值补全完成: 已提供 128 个，补全 0 个
2025-07-06 21:17:06,006 - __main__ - DEBUG - 预计算权重完成: [0.14285714285714285, 0.023809523809523808, 0.009523809523809525, 0.007142857142857143, 0.009523809523809525, 0.023809523809523808, 0.14285714285714285]
2025-07-06 21:17:06,006 - __main__ - DEBUG - 计算智能体 NAA 的Shapley值 (1/7)
2025-07-06 21:17:06,006 - __main__ - DEBUG - 智能体 NAA 的Shapley值: 0.040668
2025-07-06 21:17:06,006 - __main__ - DEBUG - 计算智能体 TAA 的Shapley值 (2/7)
2025-07-06 21:17:06,006 - __main__ - DEBUG - 智能体 TAA 的Shapley值: 0.019501
2025-07-06 21:17:06,006 - __main__ - DEBUG - 计算智能体 FAA 的Shapley值 (3/7)
2025-07-06 21:17:06,007 - __main__ - DEBUG - 智能体 FAA 的Shapley值: 0.057939
2025-07-06 21:17:06,007 - __main__ - DEBUG - 计算智能体 BOA 的Shapley值 (4/7)
2025-07-06 21:17:06,007 - __main__ - DEBUG - 智能体 BOA 的Shapley值: 0.008085
2025-07-06 21:17:06,007 - __main__ - DEBUG - 计算智能体 BeOA 的Shapley值 (5/7)
2025-07-06 21:17:06,008 - __main__ - DEBUG - 智能体 BeOA 的Shapley值: 0.007507
2025-07-06 21:17:06,008 - __main__ - DEBUG - 计算智能体 NOA 的Shapley值 (6/7)
2025-07-06 21:17:06,008 - __main__ - DEBUG - 智能体 NOA 的Shapley值: 0.033429
2025-07-06 21:17:06,008 - __main__ - DEBUG - 计算智能体 TRA 的Shapley值 (7/7)
2025-07-06 21:17:06,008 - __main__ - DEBUG - 智能体 TRA 的Shapley值: 0.049994
2025-07-06 21:17:06,009 - __main__ - DEBUG - 效率性公理验证通过: Shapley值总和 0.217123 = 大联盟值 0.217123
2025-07-06 21:17:06,009 - __main__ - INFO - Shapley值计算完成，耗时 0.005s
2025-07-06 21:17:06,009 - __main__ - INFO - ✅ 迭代Shapley值计算完成
2025-07-06 21:17:06,010 - __main__ - INFO - 🎯 识别到2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 21:17:06,010 - __main__ - INFO - 🔧 开始优化2个低表现智能体: ['BeOA', 'BOA']
2025-07-06 21:17:06,010 - __main__ - INFO - 🔧 开始OPRP优化，目标智能体: ['BeOA', 'BOA']
2025-07-06 21:17:06,010 - __main__ - INFO - 开始每周优化循环 - 周期: 2025-01-01
2025-07-06 21:17:06,011 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BeOA 优化提示词
2025-07-06 21:17:06,011 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 21:17:06,013 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:06,014 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:06,015 - httpcore.connection - DEBUG - connect_tcp.started host='127.0.0.1' port=7890 local_address=None timeout=8.0 socket_options=None
2025-07-06 21:17:06,019 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000018923101E20>
2025-07-06 21:17:06,019 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'CONNECT']>
2025-07-06 21:17:06,019 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:06,020 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'CONNECT']>
2025-07-06 21:17:06,020 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:06,021 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-06 21:17:06,021 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-06 21:17:06,021 - httpcore.proxy - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001892398BA50> server_hostname='open.bigmodel.cn' timeout=8.0
2025-07-06 21:17:06,205 - httpcore.proxy - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000018923950F50>
2025-07-06 21:17:06,205 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:06,206 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:06,206 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:06,206 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:06,206 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:06,916 - __main__ - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_211705\\prompts\\BeOA\\opt_BeOA_20250706_171524_6c6e2541.json'
2025-07-06 21:17:06,916 - contribution_assessment.assessor.ContributionAssessor - ERROR - 创建数据备份失败: [WinError 2] 系统找不到指定的文件。: 'data/backups\\backup_20250706_211705\\prompts\\BeOA\\opt_BeOA_20250706_175112_fc07205d.json'
2025-07-06 21:17:06,942 - data.comprehensive_storage_manager.ComprehensiveStorageManager - INFO - 数据备份完成: backup_20250706_211705 (0.57 MB)
2025-07-06 21:17:10,550 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117518078269565152e0073a1f2a72f3292660def301b2116d5ae1f;path=/;HttpOnly;Max-Age=1800'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062117060b69c2a2465740a4'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:10,551 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:10,552 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:10,552 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:10,553 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:10,553 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:10,554 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:10,555 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:10,557 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:10,558 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:10,559 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:10,559 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:10,559 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:10,560 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:14,348 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:15 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062117112aed3ea078b24a87'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:14,348 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:14,349 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:14,349 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:14,349 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:14,349 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:14,349 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:14,350 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:14,350 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:14,351 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:14,351 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:14,352 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:14,352 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:14,352 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:20,386 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:21 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062117151015179782694c7f'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:20,387 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:20,388 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:20,388 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:20,389 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:20,389 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:20,389 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:20,391 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:20,392 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:20,393 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:20,393 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:20,393 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:20,394 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:20,394 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:22,807 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:23 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706211721d9175e6ad6b448da'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:22,808 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:22,808 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:22,809 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:22,809 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:22,809 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:22,809 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:22,810 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:22,810 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:22,811 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:22,812 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:22,812 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:22,813 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:22,813 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:26,234 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:26 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062117235a5c51dfeffa4db5'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:26,234 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:26,234 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:26,234 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:26,234 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:26,234 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:26,235 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:26,235 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:26,235 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:26,237 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:26,237 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:26,237 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:26,237 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:26,238 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:29,615 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:30 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'202507062117261032a7f54b7b4a0d'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:29,615 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:29,617 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:29,618 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:29,618 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:29,619 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:29,619 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:29,621 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:29,621 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:29,622 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:29,623 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:29,623 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:29,623 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:29,623 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:33,088 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:33 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706211730869593501abb41ad'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:33,089 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:33,090 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:33,091 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:33,091 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:33,092 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:33,092 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:33,094 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:33,094 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:33,095 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:33,095 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:33,096 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:33,097 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:33,097 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:37,229 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:37 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706211733d21b4d7530384a01'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:37,231 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:37,231 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:37,232 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:37,232 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:37,233 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:37,233 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:37,235 - contribution_assessment.assessor.ContributionAssessor - INFO - 成功生成 8 个候选提示词
2025-07-06 21:17:37,250 - contribution_assessment.assessor.ContributionAssessor - INFO - 提示词优化记录已存储: opt_BeOA_20250706_211737_e81f05ca
2025-07-06 21:17:37,250 - contribution_assessment.assessor.ContributionAssessor - INFO - 跟踪提示词优化: BeOA -> opt_BeOA_20250706_211737_e81f05ca
2025-07-06 21:17:37,250 - contribution_assessment.assessor.ContributionAssessor - INFO - 优化记录已保存: opt_BeOA_20250706_211737_e81f05ca
2025-07-06 21:17:37,250 - contribution_assessment.assessor.ContributionAssessor - INFO - 智能体 BeOA 优化完成，最佳候选预期得分: 0.776104
2025-07-06 21:17:37,250 - __main__ - INFO - 代理 BeOA 优化完成
2025-07-06 21:17:37,250 - contribution_assessment.assessor.ContributionAssessor - INFO - 开始为智能体 BOA 优化提示词
2025-07-06 21:17:37,251 - contribution_assessment.assessor.ContributionAssessor - INFO - 生成 8 个候选提示词...
2025-07-06 21:17:37,251 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:37,252 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:37,252 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:37,253 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:37,253 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:37,253 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:37,253 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:40,883 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706211738bf91f8179a2544c3'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:40,885 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:40,885 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:40,886 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:40,886 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:40,887 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:40,887 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:40,888 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:40,889 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:40,891 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:40,891 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:40,892 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:40,893 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:40,893 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:45,622 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706211741f2bf9ea1856c43b8'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:45,623 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:45,623 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:45,624 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:45,625 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:45,625 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:45,626 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:45,629 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:45,629 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:45,631 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:45,631 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:45,631 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:45,631 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:45,632 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:47,722 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 06 Jul 2025 13:17:48 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Vary', b'Accept-Encoding'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'X-LOG-ID', b'20250706211746a31660e9a6b64d0e'), (b'Vary', b'Origin'), (b'Vary', b'Access-Control-Request-Method'), (b'Vary', b'Access-Control-Request-Headers'), (b'Strict-Transport-Security', b'max-age=31536000; includeSubDomains'), (b'Content-Encoding', b'gzip')])
2025-07-06 21:17:47,723 - httpx - INFO - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "HTTP/1.1 200 OK"
2025-07-06 21:17:47,723 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-07-06 21:17:47,724 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-07-06 21:17:47,724 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:47,725 - httpcore.http11 - DEBUG - response_closed.complete
2025-07-06 21:17:47,725 - zhipuai.core._http_client - DEBUG - HTTP Request: POST https://open.bigmodel.cn/api/paas/v4/chat/completions "200 OK"
2025-07-06 21:17:47,727 - zhipuai.api_resource.chat.completions - DEBUG - temperature:1.0, top_p:NOT_GIVEN
2025-07-06 21:17:47,728 - zhipuai.api_resource.chat.completions - DEBUG - temperature:0.99, top_p:NOT_GIVEN
2025-07-06 21:17:47,729 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-07-06 21:17:47,730 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-07-06 21:17:47,731 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-07-06 21:17:47,731 - httpcore.http11 - DEBUG - send_request_body.complete
2025-07-06 21:17:47,731 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-07-06 21:17:52,367 - httpcore.http11 - DEBUG - receive_response_headers.failed exception=KeyboardInterrupt()
2025-07-06 21:17:52,367 - httpcore.http11 - DEBUG - response_closed.started
2025-07-06 21:17:52,368 - httpcore.http11 - DEBUG - response_closed.complete
